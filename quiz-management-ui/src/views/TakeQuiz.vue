<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-black font-sans relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
      <div v-for="i in 40" :key="i"
           class="absolute rounded-full animate-float-particle"
           :class="[
             i % 3 === 0 ? 'bg-cyan-500/30' : '',
             i % 3 === 1 ? 'bg-purple-500/30' : '',
             i % 3 === 2 ? 'bg-blue-500/30' : ''
           ]"
           :style="{
             width: `${Math.random() * 10 + 2}px`,
             height: `${Math.random() * 10 + 2}px`,
             top: `${Math.random() * 100}%`,
             left: `${Math.random() * 100}%`,
             animation: `float-particle ${Math.random() * 30 + 20}s linear infinite`,
             animationDelay: `${Math.random() * 5}s`,
             opacity: Math.random() * 0.4 + 0.1
           }"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
      <div class="w-full max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500 mb-4">
            HERBIT Quiz Platform
          </h1>
          <p class="text-gray-400 text-lg">{{ assessmentName || 'Loading...' }}</p>
        </div>

        <!-- User Details Form -->
        <div v-if="!quizStarted" class="bg-gray-900/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800 shadow-2xl">
          <h2 class="text-2xl font-bold text-white mb-6 text-center">Enter Your Details</h2>

          <form @submit.prevent="startQuiz" class="space-y-6">
            <!-- Session Code Field -->
            <div>
              <label for="sessionCode" class="block text-sm font-medium text-gray-300 mb-2">
                Session Code (Optional)
              </label>
              <input
                id="sessionCode"
                v-model="existingSessionCode"
                type="text"
                maxlength="6"
                pattern="[0-9]{6}"
                @input="onSessionCodeChange"
                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-center text-lg tracking-widest"
                placeholder="123456"
              />
              <p class="text-xs text-gray-400 mt-1">
                If you have an existing session code, enter it here. Your username will be automatically filled.
              </p>
            </div>

            <!-- Username Field -->
            <div>
              <label for="username" class="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                id="username"
                v-model="username"
                type="text"
                :required="!existingSessionCode"
                :disabled="isSessionCodeValid"
                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter your username"
              />
              <p v-if="isSessionCodeValid" class="text-xs text-green-400 mt-1">
                Username automatically filled from session code
              </p>
            </div>

            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email (Optional)</label>
              <input
                id="email"
                v-model="email"
                type="email"
                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                placeholder="Enter your email"
              />
            </div>

            <Button
              type="submit"
              variant="skillGenerate"
              size="default"
              :disabled="isLoading || (!username.trim() && !existingSessionCode)"
              :loading="isLoading"
            >
              {{ isLoading ? 'Starting Quiz...' : (existingSessionCode ? 'Continue with Session' : 'Start Quiz') }}
            </Button>
          </form>

          <!-- Error Message -->
          <div v-if="errorMessage" class="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <p class="text-red-400 text-sm">{{ errorMessage }}</p>
          </div>
        </div>

        <!-- Quiz Interface -->
        <div v-else-if="!quizCompleted" class="bg-gray-900/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800 shadow-2xl">
          <!-- Quiz Header -->
          <div class="flex justify-between items-center mb-6">
            <div class="text-white">
              <h2 class="text-xl font-bold">{{ assessmentName }}</h2>
              <p class="text-gray-400">Session: <span class="text-cyan-400 font-mono">{{ sessionCode }}</span></p>
            </div>
            <div class="text-right">
              <div class="text-white text-lg font-semibold">
                Question {{ currentQuestionIndex + 1 }} of {{ maxQuestions }}
              </div>
              <div class="text-gray-400">
                Score: {{ correctAnswers }}/{{ questionsAttempted }}
              </div>
            </div>
          </div>

          <!-- Timer -->
          <div class="mb-6 text-center">
            <div class="text-2xl font-bold text-cyan-400">
              Time Remaining: {{ formatTime(timeRemaining) }}
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div
                class="bg-gradient-to-r from-cyan-500 to-blue-500 h-2 rounded-full transition-all duration-1000"
                :style="{ width: `${(timeRemaining / totalQuizTime) * 100}%` }"
              ></div>
            </div>
          </div>

          <!-- Question Display -->
          <div v-if="currentQuestion" class="mb-8">
            <div class="mb-6">
              <div class="flex items-center mb-4">
                <span
                  class="px-3 py-1 rounded-full text-xs font-semibold mr-3"
                  :class="{
                    'bg-green-500/20 text-green-400': currentQuestion.level === 'easy',
                    'bg-yellow-500/20 text-yellow-400': currentQuestion.level === 'intermediate',
                    'bg-orange-500/20 text-orange-400': currentQuestion.level === 'advanced'
                  }"
                >
                  {{ currentQuestion.level.toUpperCase() }}
                </span>
              </div>
              <h3 class="text-xl text-white font-medium leading-relaxed">
                {{ currentQuestion.question }}
              </h3>
            </div>

            <!-- Answer Options -->
            <div class="space-y-3">
              <button
                v-for="(option, key) in currentQuestion.options"
                :key="key"
                @click="selectAnswer(key)"
                :disabled="answerSubmitted || timeRemaining <= 0"
                class="w-full p-4 text-left bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-700 hover:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :class="{
                  'bg-cyan-600 border-cyan-500': selectedAnswer === key && !answerSubmitted,
                  'bg-green-600 border-green-500': answerSubmitted && key === currentQuestion.answer,
                  'bg-red-600 border-red-500': answerSubmitted && selectedAnswer === key && key !== currentQuestion.answer
                }"
              >
                <div class="flex items-center">
                  <span class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-sm font-semibold mr-4">
                    {{ key.toUpperCase() }}
                  </span>
                  <span>{{ option }}</span>
                </div>
              </button>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end items-center gap-4">
              <Button
                v-if="selectedAnswer && !answerSubmitted"
                @click="submitCurrentAnswer"
                variant="skillGenerate"
                size="skillButton"
                :disabled="!selectedAnswer || answerSubmitted || timeRemaining <= 0"
              >
                Submit Answer
              </Button>

              <Button
                @click="submitQuiz"
                variant="submitQuiz"
                size="skillButton"
              >
                Submit Quiz
              </Button>
            </div>
          </div>

          <!-- Loading State -->
          <div v-else class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto mb-4"></div>
            <p class="text-gray-400">Loading next question...</p>
          </div>

          <!-- Answer Feedback -->
          <div v-if="showFeedback" class="mt-6 p-4 rounded-lg border">
            <div v-if="lastAnswerCorrect" class="bg-green-500/10 border-green-500/30">
              <p class="text-green-400 font-semibold">✓ Correct!</p>
            </div>
            <div v-else class="bg-red-500/10 border-red-500/30">
              <p class="text-red-400 font-semibold">✗ Incorrect</p>
              <p class="text-gray-300 text-sm mt-1">
                The correct answer was: <strong>{{ getCorrectAnswerText() }}</strong>
              </p>
            </div>
          </div>
        </div>

        <!-- Quiz Results -->
        <div v-else class="bg-gray-900/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800 shadow-2xl">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-white mb-6">
              {{ timeUp ? 'Time\'s Up!' : 'Quiz Completed!' }}
            </h2>
            <div v-if="timeUp" class="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <p class="text-yellow-400 text-sm">⏰ The quiz time has expired. Your answers have been automatically submitted.</p>
            </div>

            <div class="bg-gray-800/50 p-6 rounded-lg border border-gray-700 mb-6">
              <div class="grid grid-cols-2 gap-6 text-center">
                <div>
                  <div class="text-3xl font-bold text-cyan-400">{{ correctAnswers }}</div>
                  <div class="text-gray-400">Correct Answers</div>
                </div>
                <div>
                  <div class="text-3xl font-bold text-white">{{ questionsAttempted }}</div>
                  <div class="text-gray-400">Total Questions</div>
                </div>
              </div>

              <div class="mt-6">
                <div class="text-2xl font-bold text-white mb-2">
                  Score: {{ questionsAttempted > 0 ? Math.round((correctAnswers / questionsAttempted) * 100) : 0 }}%
                </div>
                <div class="text-lg text-gray-300">
                  Performance: <span class="text-cyan-400 font-semibold">{{ getPerformanceLevel() }}</span>
                </div>
              </div>
            </div>

            <Button
              @click="restartQuiz"
              variant="skillGenerate"
              size="skillButton"
            >
              Take Another Quiz
            </Button>
          </div>
        </div>

        <!-- Assessment Info -->
        <div v-if="assessmentInfo" class="mt-6 text-center text-gray-400 text-sm">
          <p>Assessment Type: {{ assessmentInfo.is_final ? 'Final Assessment' : 'Mock Assessment' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import Button from '@/components/ui/button/Button.vue';

const route = useRoute();

// Reactive data
const assessmentId = ref(route.params.assessmentId);
const assessmentName = ref('');
const assessmentInfo = ref(null);
const username = ref('');
const email = ref('');
const isLoading = ref(false);
const errorMessage = ref('');
const quizStarted = ref(false);
const sessionCode = ref('');
const existingSessionCode = ref('');
const isSessionCodeValid = ref(false);
const sessionCodeCheckTimeout = ref(null);

// Quiz state
const quizCompleted = ref(false);
const currentQuestion = ref(null);
const currentQuestionIndex = ref(0);
const selectedAnswer = ref('');
const answerSubmitted = ref(false);
const showFeedback = ref(false);
const lastAnswerCorrect = ref(false);
const timeUp = ref(false);
const correctAnswers = ref(0);
const questionsAttempted = ref(0);
const maxQuestions = ref(20); // Default, will be updated based on assessment type
const totalQuizTime = ref(3600); // Default 60 minutes in seconds (60 * 60)
const timeRemaining = ref(3600);
const timerInterval = ref(null);
const quizStartTime = ref(null);
const currentDifficulty = ref('easy');
const difficultyProgression = ['easy', 'intermediate', 'advanced'];
const allQuestions = ref([]); // Store all questions fetched from API
const questionsLoaded = ref(false);
const questionStartTime = ref(null); // Track when current question was displayed

// Methods
const fetchAssessmentInfo = async () => {
  try {
    const response = await axios.get(`/api/admin/assessments/${assessmentId.value}`);
    assessmentInfo.value = response.data;
    assessmentName.value = response.data.name;

    // Set max questions from assessment data
    maxQuestions.value = response.data.total_questions || 30; // Use total_questions from assessment
    console.log(`Assessment max questions set to: ${maxQuestions.value}`);

    // Set quiz duration from assessment (convert minutes to seconds)
    const durationMinutes = response.data.duration_minutes || 60; // Default 60 minutes
    totalQuizTime.value = durationMinutes * 60;
    timeRemaining.value = totalQuizTime.value;

    console.log(`Assessment duration set to ${durationMinutes} minutes (${totalQuizTime.value} seconds)`);
  } catch (error) {
    console.error('Error fetching assessment info:', error);
    errorMessage.value = 'Failed to load assessment information.';
  }
};

// Quiz functionality
const startTimer = () => {
  // Only start timer if not already started
  if (!quizStartTime.value) {
    quizStartTime.value = Date.now();
    timeUp.value = false;

    timerInterval.value = setInterval(() => {
      timeRemaining.value--;

      if (timeRemaining.value <= 0) {
        clearInterval(timerInterval.value);
        timeUp.value = true;
        // End the entire quiz when time runs out
        completeQuiz();
      }
    }, 1000);
  }
};

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

const fetchAllQuestions = async () => {
  try {
    // Make sure we have both session code and username
    if (!sessionCode.value) {
      console.error('No session code available');
      errorMessage.value = 'Session code is required. Please restart the quiz.';
      return;
    }

    // Check session status before fetching questions
    try {
      console.log(`Checking session status before fetching questions: ${sessionCode.value}`);
      const sessionResponse = await axios.post('/api/check_session_code', {
        session_code: sessionCode.value
      });

      if (sessionResponse.data) {
        const sessionStatus = sessionResponse.data.session_status;

        // Check if session is completed
        if (sessionStatus === 'completed') {
          errorMessage.value = 'This session has already been completed and cannot be used again.';
          completeQuiz();
          return;
        }

        // Check if session is expired
        if (sessionStatus === 'expired') {
          errorMessage.value = 'This session has expired and cannot be used.';
          completeQuiz();
          return;
        }

        // Check if session is in a valid state (assigned or in_progress)
        if (sessionStatus !== 'assigned' && sessionStatus !== 'in_progress') {
          errorMessage.value = `Session is not available. Status: ${sessionStatus}`;
          completeQuiz();
          return;
        }
      }
    } catch (error) {
      console.error('Error checking session status before fetching questions:', error);
      if (error.response?.data?.detail) {
        errorMessage.value = error.response.data.detail;
      } else {
        errorMessage.value = 'Error validating session. Please restart the quiz.';
      }
      return;
    }

    if (!username.value) {
      // If username is missing but we have a session code, try to fetch the username
      try {
        console.log(`Fetching username for session code: ${sessionCode.value}`);
        const userResponse = await axios.get(`/api/admin/session-user/${sessionCode.value}`);
        if (userResponse.data && userResponse.data.username) {
          username.value = userResponse.data.username;
          console.log(`Username set to: ${username.value}`);
        } else {
          console.error('Username not found in response:', userResponse.data);
          errorMessage.value = 'Could not retrieve username for this session.';
          return;
        }
      } catch (error) {
        console.error('Error fetching username:', error);
        errorMessage.value = 'Error retrieving username. Please restart the quiz.';
        return;
      }
    }

    console.log('Fetching all questions with:', {
      sessionCode: sessionCode.value,
      username: username.value,
      difficulty: 'all' // Fetch all difficulty levels
    });

    const response = await axios.get(`/api/get_questions/${sessionCode.value}`, {
      params: {
        user_id: username.value,
        difficulty: 'all' // Get all questions at once
      }
    });

    console.log('Questions response:', response.data);

    // Check if response has questions
    const questions = response.data.question;
    if (!questions || questions.length === 0) {
      // No questions available, end quiz
      console.log('No questions available, completing quiz');
      completeQuiz();
      return;
    }

    allQuestions.value = questions;
    questionsLoaded.value = true;

    // Use all available questions up to the assessment's total_questions limit
    // Don't artificially limit based on available questions
    console.log(`Loaded ${questions.length} questions, assessment allows ${maxQuestions.value} questions`);
    console.log(`Will show ${Math.min(questions.length, maxQuestions.value)} questions`);

    // Start with the first question
    showNextQuestion();

  } catch (error) {
    console.error('Error fetching questions:', error);
    console.error('Error response:', error.response?.data);

    // Handle specific error cases
    if (error.response?.status === 404) {
      // No questions available
      console.log('404 error - completing quiz');
      completeQuiz();
    } else {
      const errorDetail = error.response?.data?.detail;
      const errorMsg = typeof errorDetail === 'string' ? errorDetail :
                      typeof errorDetail === 'object' ? errorDetail.error || JSON.stringify(errorDetail) :
                      error.message;
      errorMessage.value = 'Failed to load questions. ' + errorMsg;
    }
  }
};

const showNextQuestion = () => {
  // Reset question state
  selectedAnswer.value = '';
  answerSubmitted.value = false;
  showFeedback.value = false;

  // Check if we have more questions to show
  const questionsToShow = Math.min(allQuestions.value.length, maxQuestions.value);
  if (currentQuestionIndex.value >= questionsToShow) {
    console.log('No more questions to show, completing quiz');
    completeQuiz();
    return;
  }

  // Set the current question
  currentQuestion.value = allQuestions.value[currentQuestionIndex.value];
  console.log(`Showing question ${currentQuestionIndex.value + 1}/${maxQuestions.value}:`, currentQuestion.value);

  // Record the start time for this question
  questionStartTime.value = Date.now();
  console.log(`Question ${currentQuestionIndex.value + 1} start time recorded: ${questionStartTime.value}`);

  // Start timer only for the first question
  if (currentQuestionIndex.value === 0) {
    startTimer();
  }
};

const selectAnswer = (answerKey) => {
  if (answerSubmitted.value || timeRemaining.value <= 0) return;

  selectedAnswer.value = answerKey;
  // Don't auto-submit, wait for manual submit button click
};

const submitCurrentAnswer = () => {
  if (!selectedAnswer.value || answerSubmitted.value) return;
  submitAnswer(selectedAnswer.value);
};

const submitQuiz = () => {
  // Confirm before submitting the entire quiz
  if (confirm('Are you sure you want to submit the quiz? This action cannot be undone.')) {
    completeQuiz();
  }
};

const submitAnswer = async (answer) => {
  if (answerSubmitted.value) return;

  answerSubmitted.value = true;
  // Don't stop the overall timer, just continue with the quiz

  // Make sure we have both session code and username
  if (!sessionCode.value) {
    console.error('No session code available');
    errorMessage.value = 'Session code is required. Please restart the quiz.';
    return;
  }

  // Check session status before submitting answer
  try {
    console.log(`Checking session status before submitting answer: ${sessionCode.value}`);
    const sessionResponse = await axios.post('/api/check_session_code', {
      session_code: sessionCode.value
    });

    if (sessionResponse.data) {
      const sessionStatus = sessionResponse.data.session_status;

      // Check if session is completed
      if (sessionStatus === 'completed') {
        errorMessage.value = 'This session has already been completed. Cannot submit more answers.';
        completeQuiz();
        return;
      }

      // Check if session is expired
      if (sessionStatus === 'expired') {
        errorMessage.value = 'This session has expired. Cannot submit more answers.';
        completeQuiz();
        return;
      }

      // Check if session is in a valid state (in_progress)
      if (sessionStatus !== 'in_progress') {
        errorMessage.value = `Session is not in progress. Status: ${sessionStatus}`;
        completeQuiz();
        return;
      }
    }
  } catch (error) {
    console.error('Error checking session status before submitting answer:', error);
    if (error.response?.data?.detail) {
      errorMessage.value = error.response.data.detail;
    } else {
      errorMessage.value = 'Error validating session. Cannot submit answer.';
    }
    return;
  }

  if (!username.value) {
    // If username is missing but we have a session code, try to fetch the username
    try {
      console.log(`Fetching username for session code: ${sessionCode.value}`);
      const userResponse = await axios.get(`/api/admin/session-user/${sessionCode.value}`);
      if (userResponse.data && userResponse.data.username) {
        username.value = userResponse.data.username;
        console.log(`Username set to: ${username.value}`);
      } else {
        console.error('Username not found in response:', userResponse.data);
        errorMessage.value = 'Could not retrieve username for this session.';
        return;
      }
    } catch (error) {
      console.error('Error fetching username:', error);
      errorMessage.value = 'Error retrieving username. Please restart the quiz.';
      return;
    }
  }

  try {
    // Calculate time taken for this question
    const currentTime = Date.now();
    const timeTakenMs = questionStartTime.value ? currentTime - questionStartTime.value : 0;
    const timeTakenSeconds = Math.round(timeTakenMs / 1000); // Convert to seconds and round

    console.log(`Time taken for question ${currentQuestionIndex.value + 1}: ${timeTakenSeconds} seconds`);

    const answerData = {
      user_id: username.value,
      question_id: currentQuestion.value.que_id.toString(),
      answer: answer,
      session_code: sessionCode.value,
      time_taken: timeTakenSeconds
    };

    console.log('Submitting answer:', answerData);
    console.log('Current question:', currentQuestion.value);

    // First, let's test if the endpoint exists by trying a simple request
    console.log('Testing endpoint availability...');

    const response = await axios.post('/api/check_and_save_answer', answerData);

    console.log('Answer response:', response.data);

    const isCorrect = response.data.is_correct;
    lastAnswerCorrect.value = isCorrect;

    if (isCorrect) {
      correctAnswers.value++;
    }

    questionsAttempted.value++;
    showFeedback.value = true;

    // Progress difficulty based on performance (similar to CLI logic)
    updateDifficulty(isCorrect);

    // Show feedback for 3 seconds, then move to next question
    setTimeout(() => {
      const questionsToShow = Math.min(allQuestions.value.length, maxQuestions.value);
      if (questionsAttempted.value >= questionsToShow) {
        completeQuiz();
      } else {
        currentQuestionIndex.value++;
        showNextQuestion();
      }
    }, 3000);

  } catch (error) {
    console.error('Error submitting answer:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    console.error('Error config:', error.config);

    // Let's try to understand what's happening
    if (error.response?.status === 404) {
      console.error('404 Error - Endpoint not found. Checking if server is running correct version...');

      // Try to test other endpoints to see if server is running
      try {
        const testResponse = await axios.get('/api/admin/assessments');
        console.log('Other endpoints work, so server is running:', testResponse.status);
      } catch (testError) {
        console.error('Server might not be running:', testError);
      }
    }

    errorMessage.value = 'Failed to submit answer. ' + (error.response?.data?.detail || error.message);
  }
};

const updateDifficulty = (isCorrect) => {
  // Simple difficulty progression logic similar to CLI
  const currentIndex = difficultyProgression.indexOf(currentDifficulty.value);

  if (isCorrect && currentIndex < difficultyProgression.length - 1) {
    // Move to harder difficulty if correct and not at max
    currentDifficulty.value = difficultyProgression[currentIndex + 1];
  } else if (!isCorrect && currentIndex > 0) {
    // Move to easier difficulty if incorrect and not at min
    currentDifficulty.value = difficultyProgression[currentIndex - 1];
  }
};

const completeQuiz = async () => {
  stopTimer();

  // Submit the session to mark it as completed
  if (sessionCode.value && username.value) {
    try {
      console.log('Submitting session for completion:', {
        session_code: sessionCode.value,
        user_id: username.value
      });

      const response = await axios.post('/api/submit_session', {
        session_code: sessionCode.value,
        user_id: username.value
      });

      console.log('Session submission response:', response.data);

      // Update the final score if provided
      if (response.data.total_score !== undefined) {
        // You can use this score for display if needed
        console.log('Final score:', response.data.total_score);
      }

    } catch (error) {
      console.error('Error submitting session:', error);
      // Don't prevent quiz completion if submission fails
      // Just log the error and continue
    }
  }

  quizCompleted.value = true;
};

const getCorrectAnswerText = () => {
  if (!currentQuestion.value || !currentQuestion.value.options) return '';
  return currentQuestion.value.options[currentQuestion.value.answer] || '';
};

const getPerformanceLevel = () => {
  // Handle case when no questions have been attempted
  if (questionsAttempted.value === 0) return "Fail";

  const percentage = (correctAnswers.value / questionsAttempted.value) * 100;

  if (percentage === 0) return "Fail";
  if (percentage < 33) return "Basic";
  if (percentage < 62) return "Acceptable";
  if (percentage < 85) return "Exceed Expectation";
  return "OUTSTANDING";
};

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

const restartQuiz = () => {
  // Reset all quiz state
  quizCompleted.value = false;
  quizStarted.value = false;
  currentQuestion.value = null;
  currentQuestionIndex.value = 0;
  selectedAnswer.value = '';
  answerSubmitted.value = false;
  showFeedback.value = false;
  lastAnswerCorrect.value = false;
  timeUp.value = false;
  correctAnswers.value = 0;
  questionsAttempted.value = 0;
  currentDifficulty.value = 'easy';
  sessionCode.value = '';
  existingSessionCode.value = '';
  isSessionCodeValid.value = false;
  username.value = '';
  email.value = '';
  errorMessage.value = '';
  allQuestions.value = [];
  questionsLoaded.value = false;
  quizStartTime.value = null;
  questionStartTime.value = null;
  timeRemaining.value = totalQuizTime.value;
  stopTimer();
};

const checkSessionCode = async (code) => {
  if (!code || code.length !== 6) {
    isSessionCodeValid.value = false;
    username.value = '';
    return;
  }

  try {
    // First, check session status using the proper endpoint
    console.log(`Checking session code status: ${code}`);
    const sessionResponse = await axios.post('/api/check_session_code', {
      session_code: code
    });
    console.log('Session response:', sessionResponse.data);

    if (sessionResponse.data) {
      const sessionStatus = sessionResponse.data.session_status;

      // Check if session is completed
      if (sessionStatus === 'completed') {
        errorMessage.value = 'This session has already been completed and cannot be used again.';
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Check if session is expired
      if (sessionStatus === 'expired') {
        errorMessage.value = 'This session has expired and cannot be used.';
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Check if session is in a valid state (assigned or in_progress)
      if (sessionStatus !== 'assigned' && sessionStatus !== 'in_progress') {
        errorMessage.value = `Session is not available. Status: ${sessionStatus}`;
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Now fetch the username from the session code
      console.log(`Fetching username for session code: ${code}`);
      const userResponse = await axios.get(`/api/admin/session-user/${code}`);
      console.log('User response:', userResponse.data);

      if (userResponse.data && userResponse.data.username) {
        // Set the username from the response
        username.value = userResponse.data.username;
        isSessionCodeValid.value = true;
        errorMessage.value = '';
        console.log(`Username set to: ${username.value}`);

        // Update assessment info
        assessmentId.value = userResponse.data.assessment_id;
        assessmentName.value = userResponse.data.assessment_name;
        assessmentInfo.value = {
          id: userResponse.data.assessment_id,
          name: userResponse.data.assessment_name,
          is_final: false // Default to false, will be updated when quiz starts
        };

        // Also set the session code
        sessionCode.value = code;

        // Fetch full assessment details to get duration and question count
        try {
          const assessmentResponse = await axios.get(`/api/admin/assessments/${userResponse.data.assessment_id}`);
          const durationMinutes = assessmentResponse.data.duration_minutes || 60;
          totalQuizTime.value = durationMinutes * 60;
          timeRemaining.value = totalQuizTime.value;

          // Also update max questions from assessment
          maxQuestions.value = assessmentResponse.data.total_questions || 30;

          console.log(`Assessment duration set to ${durationMinutes} minutes from session code validation`);
          console.log(`Assessment max questions set to: ${maxQuestions.value} from session code validation`);
        } catch (assessmentError) {
          console.error('Error fetching assessment details:', assessmentError);
          // Use default values if fetch fails
        }

        return true;
      } else {
        console.error('Username not found in response:', userResponse.data);
        errorMessage.value = 'Could not retrieve username for this session code.';
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }
    } else {
      errorMessage.value = 'Invalid session code. Please check and try again.';
      isSessionCodeValid.value = false;
      username.value = '';
      return false;
    }
  } catch (error) {
    console.error('Error checking session code:', error);
    isSessionCodeValid.value = false;
    username.value = '';

    if (error.response?.status === 404) {
      errorMessage.value = 'Invalid session code. Please check and try again.';
    } else if (error.response?.data?.detail) {
      errorMessage.value = error.response.data.detail;
    } else {
      errorMessage.value = 'Error validating session code. Please try again.';
    }
    return false;
  }
};

const onSessionCodeChange = () => {
  // Clear previous timeout
  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  // Reset validation state
  isSessionCodeValid.value = false;
  errorMessage.value = '';

  // Only check if we have 6 digits
  if (existingSessionCode.value && existingSessionCode.value.length === 6) {
    sessionCodeCheckTimeout.value = setTimeout(() => {
      checkSessionCode(existingSessionCode.value);
    }, 500); // Debounce for 500ms
  } else {
    username.value = '';
  }
};

const testEndpoints = async () => {
  console.log('Testing backend endpoints...');

  try {
    // Test if backend is running
    const healthResponse = await axios.get('/api/admin/assessments');
    console.log('✅ Backend is running, /api/admin/assessments works:', healthResponse.status);
  } catch (error) {
    console.error('❌ Backend might not be running:', error);
    return false;
  }

  try {
    // Test the specific endpoint
    const testData = {
      user_id: 'test',
      question_id: '1',
      answer: 'a',
      session_code: '123456'
    };
    await axios.post('/api/check_and_save_answer', testData);
    console.log('✅ /check_and_save_answer endpoint exists');
  } catch (error) {
    if (error.response?.status === 404) {
      console.error('❌ /api/check_and_save_answer endpoint not found (404)');
      console.error('This suggests the server is running an older version without this endpoint');
    } else {
      console.log('✅ /api/check_and_save_answer endpoint exists (got non-404 error):', error.response?.status);
    }
  }

  return true;
};

const startQuiz = async () => {
  // Test endpoints first
  await testEndpoints();

  // If using existing session code, verify it's valid
  if (existingSessionCode.value) {
    if (!isSessionCodeValid.value) {
      // Try to validate the session code one more time
      const isValid = await checkSessionCode(existingSessionCode.value);
      if (!isValid) {
        errorMessage.value = 'Please enter a valid session code or username';
        return;
      }
    }

    // Double-check session status before starting quiz
    try {
      console.log(`Double-checking session status before starting quiz: ${existingSessionCode.value}`);
      const sessionResponse = await axios.post('/api/check_session_code', {
        session_code: existingSessionCode.value
      });

      if (sessionResponse.data) {
        const sessionStatus = sessionResponse.data.session_status;

        // Check if session is completed
        if (sessionStatus === 'completed') {
          errorMessage.value = 'This session has already been completed and cannot be used again.';
          return;
        }

        // Check if session is expired
        if (sessionStatus === 'expired') {
          errorMessage.value = 'This session has expired and cannot be used.';
          return;
        }

        // Check if session is in a valid state (assigned or in_progress)
        if (sessionStatus !== 'assigned' && sessionStatus !== 'in_progress') {
          errorMessage.value = `Session is not available. Status: ${sessionStatus}`;
          return;
        }
      }
    } catch (error) {
      console.error('Error double-checking session status:', error);
      if (error.response?.data?.detail) {
        errorMessage.value = error.response.data.detail;
      } else {
        errorMessage.value = 'Error validating session. Please try again.';
      }
      return;
    }

    // Session code is valid, proceed with the quiz
    console.log(`Starting quiz with session code: ${sessionCode.value} and username: ${username.value}`);
    quizStarted.value = true;

    // Start the first question
    setTimeout(() => {
      fetchAllQuestions();
    }, 1000);
    return;
  }

  // Otherwise, create a new session with the provided username
  if (!username.value.trim()) {
    errorMessage.value = 'Please enter your username';
    return;
  }

  isLoading.value = true;
  errorMessage.value = '';

  try {
    console.log('Creating session with:', {
      assessment_id: parseInt(assessmentId.value),
      usernames: username.value.trim()
    });

    const response = await axios.post('/api/admin/sessions', {
      assessment_id: parseInt(assessmentId.value),
      usernames: username.value.trim()
    });

    console.log('Session creation response:', response.data);

    if (response.data.sessions && response.data.sessions.length > 0) {
      sessionCode.value = response.data.sessions[0].sessionCode;
      console.log('Session code set to:', sessionCode.value);
      quizStarted.value = true;

      // Start the first question
      setTimeout(() => {
        fetchAllQuestions();
      }, 1000);
    } else {
      errorMessage.value = 'Failed to create quiz session. Please try again.';
    }
  } catch (error) {
    console.error('Error starting quiz:', error);
    errorMessage.value = error.response?.data?.detail || 'Failed to start quiz. Please try again.';
  } finally {
    isLoading.value = false;
  }
};

const copySessionCode = async () => {
  try {
    await navigator.clipboard.writeText(sessionCode.value);
    // Could add a toast notification here
  } catch (error) {
    console.error('Failed to copy session code:', error);
  }
};

// Lifecycle
onMounted(() => {
  if (assessmentId.value) {
    fetchAssessmentInfo();
  } else {
    errorMessage.value = 'Invalid assessment link.';
  }
});

onUnmounted(() => {
  // Clean up timer when component is destroyed
  stopTimer();
  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }
});
</script>

<style scoped>
@keyframes float-particle {
  0% {
    transform: translateY(0) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
    transform: translateY(-10vh) rotate(45deg) scale(1);
  }
  50% {
    transform: translateY(-50vh) rotate(180deg) scale(1.2);
    opacity: 0.6;
  }
  90% {
    opacity: 0.2;
    transform: translateY(-90vh) rotate(315deg) scale(1);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg) scale(0.8);
    opacity: 0;
  }
}
</style>
